package com.midas.crm.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.*;
import java.security.cert.X509Certificate;

/**
 * Configuración para RestTemplate con manejo de SSL
 */
@Configuration
@Slf4j
public class RestTemplateConfig {

    /**
     * RestTemplate que ignora certificados SSL (solo para desarrollo/testing)
     */
    @Bean("sslIgnoringRestTemplate")
    public RestTemplate sslIgnoringRestTemplate() {
        try {
            // Crear un TrustManager que acepta todos los certificados
            TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() {
                        return null;
                    }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {
                    }
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {
                    }
                }
            };

            // Instalar el TrustManager que acepta todos
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

            // Crear un HostnameVerifier que acepta todos los hostnames
            HostnameVerifier allHostsValid = new HostnameVerifier() {
                public boolean verify(String hostname, SSLSession session) {
                    return true;
                }
            };

            // Instalar el HostnameVerifier que acepta todos
            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);

            log.warn("RestTemplate configurado para ignorar certificados SSL - SOLO PARA DESARROLLO");
            return new RestTemplate();

        } catch (Exception e) {
            log.error("Error al configurar RestTemplate SSL-ignorante", e);
            return new RestTemplate();
        }
    }

    /**
     * RestTemplate estándar para uso general
     */
    @Bean("standardRestTemplate")
    public RestTemplate standardRestTemplate() {
        return new RestTemplate();
    }
}
