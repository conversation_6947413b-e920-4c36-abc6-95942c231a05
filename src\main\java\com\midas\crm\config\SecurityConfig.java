package com.midas.crm.config;

import com.midas.crm.security.jwt.JwtAuthorizationFilter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

/**
 * Configuración de seguridad para la aplicación MIDAS CRM
 * Define políticas de acceso a recursos y configuración de seguridad HTTP
 */
@EnableWebSecurity
@Configuration
public class SecurityConfig {
    // Rutas de API para autenticación
    @Value("${api.route.authentication}")
    private String authRoute;

    // Rutas de API para usuarios
    @Value("${api.route.user}")
    private String userRoute;

    // Rutas de API para clientes
    @Value("${api.route.clientes}")
    private String clientesRoute;
    @Value("${api.route.cliente-promocion}")
    private String clientePromocionRoute;

    // Rutas de API para anuncios
    @Value("${api.route.anuncios}")
    private String anunciosRoute;

    // Rutas de API para manuales
    @Value("${api.route.manuales}")
    private String manualesRoute;

    // Rutas de API para coordinadores y asesores
    @Value("${api.route.coordinadores}")
    private String coordinadoresRoute;
    @Value("${api.route.asesores}")
    private String asesoresRoute;

    // Rutas de API para estadísticas por sede
    @Value("${api.route.estadisticas-sede}")
    private String estadisticasSedeRoute;

    // Rutas de API para calendario
    @Value("${api.route.calendar}")
    private String calendarRoute;

    // Rutas de API para mensajería
    @Value("${api.route.sms}")
    private String smsRoute;
    @Value("${api.route.fcm}")
    private String fcmRoute;
    @Value("${api.route.messaging-token}")
    private String messagingTokenRoute;
    @Value("${api.route.numbers}")
    private String numbersRoute;
    @Value("${api.route.bulk}")
    private String bulkRoute;

    // Rutas de API para IP permitidas
    @Value("${api.route.ip-allowed}")
    private String ipAllowedRoute;

    // Rutas de API para notificaciones
    @Value("${api.route.notifications}")
    private String notificationsRoute;

    // Rutas de API para FAQs
    @Value("${api.route.faqs}")
    private String faqsRoute;
    @Value("${api.route.faq-respuestas}")
    private String faqRespuestasRoute;

    // Rutas de API para monitoreo del sistema
    @Value("${api.route.system.connections}")
    private String systemConnectionsRoute;

    // Rutas de API para cursos
    @Value("${api.route.curso}")
    private String cursoRoute;
    @Value("${api.route.modulo}")
    private String moduloRoute;
    @Value("${api.route.leccion}")
    private String leccionRoute;
    @Value("${api.route.progreso}")
    private String progresoRoute;
    @Value("${api.route.video-info}")
    private String videoInfoRoute;
    @Value("${api.route.cursos-usuarios}")
    private String cursosUsuariosRoute;
    @Value("${api.route.secciones}")
    private String seccionesRoute;

    // Rutas de API para cuestionarios
    @Value("${api.route.cuestionario}")
    private String cuestionarioRoute;
    @Value("${api.route.pregunta}")
    private String preguntaRoute;
    @Value("${api.route.respuesta}")
    private String respuestaRoute;
    @Value("${api.route.respuesta-usuario}")
    private String respuestaUsuarioRoute;

    // Ruta para API del Catastro
    @Value("${api.route.catastro}")
    private String catastroRoute;

    // Rutas de API para encuestas
    @Value("${api.route.encuestas}")
    private String encuestasRoute;
    @Value("${api.route.preguntas-encuesta}")
    private String preguntasEncuestaRoute;
    @Value("${api.route.respuestas-encuesta}")
    private String respuestasEncuestaRoute;

    // Ruta para subtítulos proxy
    private String subtitulosProxyRoute = "/api/subtitulos-proxy";

    // Ruta para PDF proxy
    private String pdfProxyRoute = "/api/pdf-proxy";

    @Value("${api.route.roles}")
    private String rolesRoute;

    @Value("${api.route.routes}")
    private String routesRoute;

    // Rutas de API para Google Drive
    @Value("${api.route.google-drive}")
    private String googleDriveRoute;

    @Value("${api.route.transcription-queue}")
    private String rabbitMqRoute;

    /**
     * Configura el codificador de contraseñas para la aplicación
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    /**
     * Crea el filtro de autorización JWT
     */
    @Bean
    public JwtAuthorizationFilter jwtAuthorizationFilter() {
        return new JwtAuthorizationFilter();
    }

    /**
     * Método auxiliar para crear un array de patrones de URL para todas las rutas
     * de API
     *
     * @return Array de strings con todas las rutas de API configuradas
     */
    private String[] getAllApiRoutes() {
        return new String[] {
                authRoute + "/**",
                userRoute + "/**",
                clientesRoute + "/**",
                clientePromocionRoute + "/**",
                anunciosRoute + "/**",
                manualesRoute + "/**",
                coordinadoresRoute + "/**",
                asesoresRoute + "/**",
                estadisticasSedeRoute + "/**",
                calendarRoute + "/**",
                smsRoute + "/**",
                fcmRoute + "/**",
                messagingTokenRoute + "/**",
                numbersRoute + "/**",
                bulkRoute + "/**",
                ipAllowedRoute + "/**",
                notificationsRoute + "/**",
                faqsRoute + "/**",
                faqRespuestasRoute + "/**",
                systemConnectionsRoute + "/**",
                cursoRoute + "/**",
                moduloRoute + "/**",
                leccionRoute + "/**",
                progresoRoute + "/**",
                videoInfoRoute + "/**",
                cursosUsuariosRoute + "/**",
                seccionesRoute + "/**",
                // Nuevas rutas para cuestionarios
                cuestionarioRoute + "/**",
                preguntaRoute + "/**",
                respuestaRoute + "/**",
                respuestaUsuarioRoute + "/**",
                // Ruta para API del Catastro
                catastroRoute + "/**",
                // Rutas para encuestas
                encuestasRoute + "/**",
                preguntasEncuestaRoute + "/**",
                respuestasEncuestaRoute + "/**",
                // Ruta para subtítulos proxy
                subtitulosProxyRoute + "/**",
                // Ruta para PDF proxy
                pdfProxyRoute + "/**",
                rolesRoute + "/**",
                routesRoute + "/**",
                // Ruta para Google Drive
                googleDriveRoute + "/**",
                rabbitMqRoute + "/**"
        };
    }

    /**
     * Configura los filtros de seguridad HTTP con políticas de acceso optimizadas
     *
     * @param http                   La configuración de seguridad HTTP
     * @param jwtAuthorizationFilter El filtro de autorización JWT
     * @return La cadena de filtros de seguridad configurada
     * @throws Exception Si ocurre un error durante la configuración
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http, JwtAuthorizationFilter jwtAuthorizationFilter)
            throws Exception {
        return http
                .cors(cors -> cors.configurationSource(corsConfigurationSource()))
                .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                .authorizeHttpRequests(auth -> auth
                        // Permitir acceso a la raíz y WebSockets sin autenticación
                        .requestMatchers("/", "/ws/**").permitAll()
                        // Permitir acceso a la ruta de autenticación sin autenticación
                        .requestMatchers(authRoute + "/**").permitAll()
                        // Permitir acceso a las rutas de health check sin autenticación
                        .requestMatchers("/api/health/**").permitAll()
                        // Permitir acceso a las rutas de OAuth2 sin autenticación
                        .requestMatchers("/oauth2/**").permitAll()
                        // Permitir acceso a las rutas de API Proxy sin autenticación
                        .requestMatchers(numbersRoute + "/**", bulkRoute + "/**", catastroRoute + "/**").permitAll()
                        // Configurar acceso a todas las demás rutas de API (requieren autenticación)
                        .requestMatchers(getAllApiRoutes()).authenticated()
                        // Cualquier otra solicitud también requiere autenticación
                        .anyRequest().authenticated())
                .addFilterBefore(jwtAuthorizationFilter, UsernamePasswordAuthenticationFilter.class)
                .csrf(csrf -> csrf.disable())
                .build();
    }

    /**
     * Configura la fuente de configuración CORS para la aplicación
     */
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration config = new CorsConfiguration();
        config.setAllowCredentials(true);
        config.addAllowedOriginPattern("*");
        config.addAllowedHeader("*");
        config.addAllowedMethod("*");

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", config);
        return source;
    }

    /**
     * Expone el AuthenticationManager como un bean para ser utilizado en otros
     * componentes
     *
     * @param authConfig Configuración de autenticación
     * @return AuthenticationManager configurado
     * @throws Exception Si ocurre un error al obtener el AuthenticationManager
     */
    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration authConfig) throws Exception {
        return authConfig.getAuthenticationManager();
    }
}
