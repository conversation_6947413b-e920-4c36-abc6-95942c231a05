# Configuraci�n base para todos los entornos
spring.application.name=crm

# Por defecto, usar el perfil de desarrollo
# Para cambiar a producci�n, usar: -Dspring.profiles.active=prod
spring.profiles.active=prod

# Permitir dependencias circulares (soluci�n temporal)
spring.main.allow-circular-references=true

# JWT
app.jwt.secret=RandomSecretKey123456789!RandomSecretKey123456789!RandomSecretKey123456789!
app.jwt.expiration-in-ms=561600000

# Seguridad por clave secreta (servicio interno)
service.security.secure-key-username=vaxiDrezKeySecureUsername
service.security.secure-key-password=vaxiDrezKeySecurePassword!

# Configuraci�n para la documentaci�n de la API
# No se requiere configuraci�n adicional para nuestra implementaci�n personalizada

# Rutas de API para controladores
# Autenticaci�n
api.route.authentication=/api/authentication

# Usuarios
api.route.user=/api/user

# Clientes
api.route.clientes=/api/clientes
api.route.cliente-promocion=/api/cliente-promocion

# Anuncios
api.route.anuncios=/api/anuncios

# Manuales
api.route.manuales=/api/manuales

# Coordinadores
api.route.coordinadores=/api/coordinadores

# Asesores
api.route.asesores=/api/asesores

# Estad�sticas por sede
api.route.estadisticas-sede=/api/estadisticas-sede

# Calendario
api.route.calendar=/api/calendar

# Mensajer�a
api.route.sms=/api/sms
api.route.fcm=/api/fcm
api.route.messaging-token=/api/registerMessagingToken
api.route.numbers=/api/numbers
api.route.bulk=/api/bulk

# IP permitidas
api.route.ip-allowed=/api/ip-allowed

# Notificaciones
api.route.notifications=/api/notifications

# FAQs
api.route.faqs=/api/faqs
api.route.faq-respuestas=/api/faq-respuestas

# Monitoreo del sistema
api.route.system.connections=/api/system/connections

#CURSOS
#CURSOS
api.route.curso=/api/cursos
api.route.modulo=/api/modulos
api.route.leccion=/api/lecciones
api.route.progreso=/api/progreso
api.route.secciones=/api/secciones
api.route.video-info=/api/video-info
api.route.cursos-usuarios=/api/cursos-usuarios

#CUESTIONARIOS
api.route.cuestionario=/api/cuestionarios
api.route.pregunta=/api/preguntas
api.route.respuesta=/api/respuestas
api.route.respuesta-usuario=/api/respuestas-usuario

#CATASTRO
api.route.catastro=/api/catastro

#ENCUESTAS
api.route.encuestas=/api/encuestas
api.route.preguntas-encuesta=/api/preguntas-encuesta
api.route.respuestas-encuesta=/api/respuestas-encuesta

#ROLES Y RUTAS
api.route.roles=/api/roles
api.route.routes=/api/routes

# Google Drive API
api.route.google-drive=/api/google-drive
api.route.transcription-queue=/api/transcription-queue

# Google Drive Configuration
google.drive.auth.type=oauth
google.drive.credentials.file=client_secret_com.json
google.drive.service-account.file=audios-461713-c7a956b2b4ef.json


# Configuraci�n para compartir autom�tico
google.drive.shared.email=<EMAIL>
google.drive.auto-share.enabled=false

# Configuraci�n de conversi�n de audio
audio.conversion.temp.dir=${java.io.tmpdir}/audio-conversion
audio.conversion.ffmpeg.path=ffmpeg
audio.conversion.upload.to.drive=true
audio.conversion.drive.folder.name=CONVERTED_MP3

# ID de carpeta padre compartida (opcional)
# Esta carpeta debe ser creada manualmente y compartida con ambas cuentas
google.drive.shared.parent.folder.id=


# Configuraci�n de conexi�n
spring.rabbitmq.connection-timeout=30000
spring.rabbitmq.requested-heartbeat=60

# Configuraci�n de publisher
spring.rabbitmq.publisher-confirm-type=correlated
spring.rabbitmq.publisher-returns=true

# Configuraci�n de listener
spring.rabbitmq.listener.simple.acknowledge-mode=manual
spring.rabbitmq.listener.simple.retry.enabled=true
spring.rabbitmq.listener.simple.retry.max-attempts=3

# ===== CONFIGURACI�N DE APIS EXTERNAS =====
# URLs de las APIs de transcripci�n y comparaci�n
transcription.api.url=https://apisozarusac.com/BackendTranscriptor/api
comparison.api.url=https://apisozarusac.com/ventas/api

# Configuraci�n de timeouts para APIs externas
transcription.api.timeout=300000
comparison.api.timeout=60000