package com.midas.crm.controller;

import com.midas.crm.service.GoogleDriveService;
import com.midas.crm.service.serviceImpl.GoogleDriveServiceImpl;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.GenericResponseConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Controlador para operaciones con Google Drive
 */
@RestController
@RequestMapping("${api.route.google-drive}")
@RequiredArgsConstructor
@Slf4j
public class GoogleDriveController {

    private final GoogleDriveService googleDriveService;
    private final GoogleDriveServiceImpl googleDriveServiceImpl; // Para acceder a métodos específicos

    /**
     * Sube un archivo a Google Drive
     */
    @PostMapping("/upload")
    public ResponseEntity<GenericResponse<Map<String, String>>> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "fileName", required = false) String fileName,
            @RequestParam(value = "folderId", required = false) String folderId) {

        try {
            if (file.isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(new GenericResponse<>(GenericResponseConstants.ERROR,
                                "El archivo está vacío", null));
            }

            String finalFileName = fileName != null ? fileName : file.getOriginalFilename();
            String fileId = googleDriveService.uploadFile(file, finalFileName, folderId);

            Map<String, String> response = new HashMap<>();
            response.put("fileId", fileId);
            response.put("fileName", finalFileName);
            response.put("message", "Archivo subido exitosamente");

            return ResponseEntity.ok(new GenericResponse<>(GenericResponseConstants.SUCCESS,
                    "Archivo subido a Google Drive", response));

        } catch (Exception e) {
            //   log.error("Error al subir archivo a Google Drive: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR,
                            "Error al subir archivo: " + e.getMessage(), null));
        }
    }

    /**
     * Descarga un archivo de Google Drive
     */
    @GetMapping("/download/{fileId}")
    public ResponseEntity<?> downloadFile(@PathVariable String fileId) {
        try {
            byte[] fileContent = googleDriveService.downloadFile(fileId);
            Map<String, Object> fileInfo = googleDriveService.getFileInfo(fileId);

            String fileName = (String) fileInfo.get("name");
            String mimeType = (String) fileInfo.get("mimeType");

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType(mimeType != null ? mimeType : "application/octet-stream"));
            headers.setContentDispositionFormData("attachment", fileName);
            headers.setContentLength(fileContent.length);

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(fileContent);

        } catch (Exception e) {
            //     log.error("Error al descargar archivo de Google Drive: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR,
                            "Error al descargar archivo: " + e.getMessage(), null));
        }
    }

    /**
     * Elimina un archivo de Google Drive
     */
    @DeleteMapping("/{fileId}")
    public ResponseEntity<GenericResponse<String>> deleteFile(@PathVariable String fileId) {
        try {
            googleDriveService.deleteFile(fileId);
            return ResponseEntity.ok(new GenericResponse<>(GenericResponseConstants.SUCCESS,
                    "Archivo eliminado exitosamente", "Archivo con ID " + fileId + " eliminado"));

        } catch (Exception e) {
            //    log.error("Error al eliminar archivo de Google Drive: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR,
                            "Error al eliminar archivo: " + e.getMessage(), null));
        }
    }

    /**
     * Lista archivos en Google Drive
     */
    @GetMapping("/files")
    public ResponseEntity<GenericResponse<List<Map<String, Object>>>> listFiles(
            @RequestParam(value = "folderId", required = false) String folderId,
            @RequestParam(value = "pageSize", defaultValue = "10") int pageSize) {

        try {
            List<Map<String, Object>> files = googleDriveService.listFiles(folderId, pageSize);
            return ResponseEntity.ok(new GenericResponse<>(GenericResponseConstants.SUCCESS,
                    "Archivos obtenidos exitosamente", files));

        } catch (Exception e) {
            //    log.error("Error al listar archivos de Google Drive: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR,
                            "Error al listar archivos: " + e.getMessage(), null));
        }
    }

    /**
     * Crea una carpeta en Google Drive
     */
    @PostMapping("/folder")
    public ResponseEntity<GenericResponse<Map<String, String>>> createFolder(
            @RequestParam("folderName") String folderName,
            @RequestParam(value = "parentFolderId", required = false) String parentFolderId) {

        try {
            String folderId = googleDriveService.createFolder(folderName, parentFolderId);

            Map<String, String> response = new HashMap<>();
            response.put("folderId", folderId);
            response.put("folderName", folderName);
            response.put("message", "Carpeta creada exitosamente");

            return ResponseEntity.ok(new GenericResponse<>(GenericResponseConstants.SUCCESS,
                    "Carpeta creada en Google Drive", response));

        } catch (Exception e) {
            //   log.error("Error al crear carpeta en Google Drive: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR,
                            "Error al crear carpeta: " + e.getMessage(), null));
        }
    }

    /**
     * Crea una carpeta compartida (busca primero, si no existe la crea)
     */
    @PostMapping("/folder/shared")
    public ResponseEntity<GenericResponse<Map<String, String>>> createSharedFolder(
            @RequestParam("folderName") String folderName,
            @RequestParam("userEmail") String userEmail) {

        try {
            String folderId = googleDriveServiceImpl.findOrCreateSharedFolder(folderName, userEmail);

            Map<String, String> response = new HashMap<>();
            response.put("folderId", folderId);
            response.put("folderName", folderName);
            response.put("userEmail", userEmail);
            response.put("message", "Carpeta compartida creada/encontrada exitosamente");

            return ResponseEntity.ok(new GenericResponse<>(GenericResponseConstants.SUCCESS,
                    "Carpeta compartida procesada", response));

        } catch (Exception e) {
            //    log.error("Error al crear carpeta compartida: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR,
                            "Error al crear carpeta compartida: " + e.getMessage(), null));
        }
    }

    /**
     * Crea una carpeta en una ubicación compartida específica
     */
    @PostMapping("/folder/in-shared-location")
    public ResponseEntity<GenericResponse<Map<String, String>>> createFolderInSharedLocation(
            @RequestParam("folderName") String folderName,
            @RequestParam("sharedParentFolderId") String sharedParentFolderId) {

        try {
            String folderId = googleDriveServiceImpl.createFolderInSharedLocation(folderName, sharedParentFolderId);

            Map<String, String> response = new HashMap<>();
            response.put("folderId", folderId);
            response.put("folderName", folderName);
            response.put("parentFolderId", sharedParentFolderId);
            response.put("message", "Carpeta creada en ubicación compartida");

            return ResponseEntity.ok(new GenericResponse<>(GenericResponseConstants.SUCCESS,
                    "Carpeta creada en ubicación compartida", response));

        } catch (Exception e) {
            //   log.error("Error al crear carpeta en ubicación compartida: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR,
                            "Error al crear carpeta en ubicación compartida: " + e.getMessage(), null));
        }
    }

    /**
     * Obtiene información de un archivo
     */
    @GetMapping("/info/{fileId}")
    public ResponseEntity<GenericResponse<Map<String, Object>>> getFileInfo(@PathVariable String fileId) {
        try {
            Map<String, Object> fileInfo = googleDriveService.getFileInfo(fileId);
            return ResponseEntity.ok(new GenericResponse<>(GenericResponseConstants.SUCCESS,
                    "Información del archivo obtenida", fileInfo));

        } catch (Exception e) {
            //    log.error("Error al obtener información del archivo: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR,
                            "Error al obtener información: " + e.getMessage(), null));
        }
    }

    /**
     * Comparte un archivo
     */
    @PostMapping("/share/{fileId}")
    public ResponseEntity<GenericResponse<Map<String, String>>> shareFile(
            @PathVariable String fileId,
            @RequestParam("email") String email,
            @RequestParam(value = "role", defaultValue = "reader") String role) {

        try {
            String permissionId = googleDriveService.shareFile(fileId, email, role);

            Map<String, String> response = new HashMap<>();
            response.put("permissionId", permissionId);
            response.put("email", email);
            response.put("role", role);
            response.put("message", "Archivo compartido exitosamente");

            return ResponseEntity.ok(new GenericResponse<>(GenericResponseConstants.SUCCESS,
                    "Archivo compartido", response));

        } catch (Exception e) {
            //     log.error("Error al compartir archivo: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR,
                            "Error al compartir archivo: " + e.getMessage(), null));
        }
    }

    /**
     * Obtiene enlace público de un archivo
     */
    @PostMapping("/public-link/{fileId}")
    public ResponseEntity<GenericResponse<Map<String, String>>> getPublicLink(@PathVariable String fileId) {
        try {
            String publicLink = googleDriveService.getPublicLink(fileId);

            Map<String, String> response = new HashMap<>();
            response.put("fileId", fileId);
            response.put("publicLink", publicLink);
            response.put("message", "Enlace público generado");

            return ResponseEntity.ok(new GenericResponse<>(GenericResponseConstants.SUCCESS,
                    "Enlace público obtenido", response));

        } catch (Exception e) {
            //      log.error("Error al generar enlace público: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR,
                            "Error al generar enlace público: " + e.getMessage(), null));
        }
    }

    /**
     * Busca una carpeta por nombre
     */
    @GetMapping("/find-folder/{folderName}")
    public ResponseEntity<GenericResponse<Map<String, String>>> findFolder(@PathVariable String folderName) {
        try {
            String folderId = googleDriveService.findFolderByName(folderName);

            if (folderId != null) {
                Map<String, String> response = new HashMap<>();
                response.put("folderId", folderId);
                response.put("folderName", folderName);
                response.put("message", "Carpeta encontrada");

                return ResponseEntity.ok(new GenericResponse<>(GenericResponseConstants.SUCCESS,
                        "Carpeta encontrada", response));
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(new GenericResponse<>(GenericResponseConstants.ERROR,
                                "Carpeta no encontrada", null));
            }

        } catch (Exception e) {
            //  log.error("Error al buscar carpeta: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR,
                            "Error al buscar carpeta: " + e.getMessage(), null));
        }
    }

    /**
     * Lista todas las carpetas disponibles
     */
    @GetMapping("/folders")
    public ResponseEntity<GenericResponse<List<Map<String, Object>>>> listFolders(
            @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {

        try {
            List<Map<String, Object>> folders = googleDriveService.listFolders(pageSize);
            return ResponseEntity.ok(new GenericResponse<>(GenericResponseConstants.SUCCESS,
                    "Carpetas obtenidas exitosamente", folders));

        } catch (Exception e) {
            //   log.error("Error al listar carpetas: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR,
                            "Error al listar carpetas: " + e.getMessage(), null));
        }
    }

    /**
     * Busca archivos MP3 por número de agente
     */
    @GetMapping("/find-mp3/{numeroAgente}")
    public ResponseEntity<GenericResponse<List<Map<String, Object>>>> findMp3FilesByAgent(@PathVariable String numeroAgente) {
        try {
            List<Map<String, Object>> files = googleDriveService.findMp3FilesByAgent(numeroAgente);

            if (!files.isEmpty()) {
                return ResponseEntity.ok(new GenericResponse<>(GenericResponseConstants.SUCCESS,
                        "Archivos MP3 encontrados para agente " + numeroAgente, files));
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(new GenericResponse<>(GenericResponseConstants.ERROR,
                                "No se encontraron archivos MP3 para el agente " + numeroAgente, null));
            }

        } catch (Exception e) {
            log.error("Error al buscar archivos MP3 para agente {}: {}", numeroAgente, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR,
                            "Error al buscar archivos MP3: " + e.getMessage(), null));
        }
    }

    /**
     * Obtiene URL de descarga de archivo MP3 por número de agente
     */
    @GetMapping("/mp3-url/{numeroAgente}")
    public ResponseEntity<GenericResponse<Map<String, String>>> getMp3UrlByAgent(
            @PathVariable String numeroAgente,
            @RequestParam(value = "folderId", required = false) String folderId) {
        try {
            String mp3Url = googleDriveService.findMp3UrlByAgent(numeroAgente, folderId);

            if (mp3Url != null) {
                Map<String, String> response = new HashMap<>();
                response.put("numeroAgente", numeroAgente);
                response.put("mp3Url", mp3Url);
                response.put("message", "Archivo MP3 encontrado");

                return ResponseEntity.ok(new GenericResponse<>(GenericResponseConstants.SUCCESS,
                        "URL de archivo MP3 obtenida", response));
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(new GenericResponse<>(GenericResponseConstants.ERROR,
                                "No se encontró archivo MP3 para el agente " + numeroAgente, null));
            }

        } catch (Exception e) {
            // log.error("Error al obtener URL de MP3 para agente {}: {}", numeroAgente, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR,
                            "Error al obtener URL de MP3: " + e.getMessage(), null));
        }
    }

    /**
     * Busca archivo de audio por número móvil y agente (más preciso)
     */
    @GetMapping("/audio-url")
    public ResponseEntity<GenericResponse<Map<String, String>>> getAudioUrlByMovilAndAgent(
            @RequestParam("numeroMovil") String numeroMovil,
            @RequestParam("numeroAgente") String numeroAgente,
            @RequestParam(value = "folderId", required = false) String folderId) {
        try {
            log.info("Buscando archivo de audio para móvil {} y agente {} en carpeta {}",
                    numeroMovil, numeroAgente, folderId != null ? folderId : "todas");

            String audioUrl = googleDriveServiceImpl.findAudioByMovilAndAgent(numeroMovil, numeroAgente, folderId);

            if (audioUrl != null) {
                Map<String, String> response = new HashMap<>();
                response.put("numeroMovil", numeroMovil);
                response.put("numeroAgente", numeroAgente);
                response.put("audioUrl", audioUrl);
                response.put("message", "Archivo de audio encontrado");

                log.info("Archivo de audio encontrado exitosamente para móvil {} y agente {}",
                        numeroMovil, numeroAgente);

                return ResponseEntity.ok(new GenericResponse<>(GenericResponseConstants.SUCCESS,
                        "URL de archivo de audio obtenida", response));
            } else {
                log.warn("No se encontró archivo de audio para móvil {} y agente {}",
                        numeroMovil, numeroAgente);

                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(new GenericResponse<>(GenericResponseConstants.ERROR,
                                "No se encontró archivo de audio para móvil " + numeroMovil + " y agente " + numeroAgente, null));
            }

        } catch (Exception e) {
            log.error("Error al obtener URL de audio para móvil {} y agente {}: {}",
                    numeroMovil, numeroAgente, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR,
                            "Error al obtener URL de audio: " + e.getMessage(), null));
        }
    }

    /**
     * Busca archivo de audio sistemáticamente en todas las carpetas
     */
    @GetMapping("/audio-url/search-all")
    public ResponseEntity<GenericResponse<Map<String, String>>> searchAudioInAllFolders(
            @RequestParam("numeroMovil") String numeroMovil,
            @RequestParam("numeroAgente") String numeroAgente) {
        try {
            log.info("Iniciando búsqueda sistemática en todas las carpetas para móvil {} y agente {}",
                    numeroMovil, numeroAgente);

            // Forzar búsqueda en todas las carpetas pasando null como folderId
            String audioUrl = googleDriveServiceImpl.findAudioByMovilAndAgent(numeroMovil, numeroAgente, null);

            if (audioUrl != null) {
                Map<String, String> response = new HashMap<>();
                response.put("numeroMovil", numeroMovil);
                response.put("numeroAgente", numeroAgente);
                response.put("audioUrl", audioUrl);
                response.put("message", "Archivo de audio encontrado mediante búsqueda sistemática");

                log.info("Archivo de audio encontrado mediante búsqueda sistemática para móvil {} y agente {}",
                        numeroMovil, numeroAgente);

                return ResponseEntity.ok(new GenericResponse<>(GenericResponseConstants.SUCCESS,
                        "URL de archivo de audio obtenida mediante búsqueda sistemática", response));
            } else {
                log.warn("No se encontró archivo de audio en ninguna carpeta para móvil {} y agente {}",
                        numeroMovil, numeroAgente);

                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(new GenericResponse<>(GenericResponseConstants.ERROR,
                                "No se encontró archivo de audio en ninguna carpeta para móvil " + numeroMovil + " y agente " + numeroAgente, null));
            }

        } catch (Exception e) {
            log.error("Error durante búsqueda sistemática para móvil {} y agente {}: {}",
                    numeroMovil, numeroAgente, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(GenericResponseConstants.ERROR,
                            "Error durante búsqueda sistemática: " + e.getMessage(), null));
        }
    }
}