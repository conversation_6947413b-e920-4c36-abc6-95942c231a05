package com.midas.crm.service;

import java.io.IOException;

/**
 * Servicio para conversión de archivos de audio
 */
public interface AudioConversionService {
    
    /**
     * Convierte un archivo GSM a MP3
     * @param gsmFileUrl URL del archivo GSM a convertir
     * @param outputFileName Nombre del archivo de salida (sin extensión)
     * @return URL del archivo MP3 convertido
     * @throws IOException Si hay error en la conversión
     */
    String convertGsmToMp3(String gsmFileUrl, String outputFileName) throws IOException;
    
    /**
     * Verifica si FFmpeg está disponible en el sistema
     * @return true si FFmpeg está disponible
     */
    boolean isFFmpegAvailable();
    
    /**
     * Descarga un archivo desde una URL
     * @param fileUrl URL del archivo
     * @param localPath Ruta local donde guardar el archivo
     * @throws IOException Si hay error en la descarga
     */
    void downloadFile(String fileUrl, String localPath) throws IOException;
    
    /**
     * Elimina archivos temporales
     * @param filePath Ruta del archivo a eliminar
     */
    void cleanupTempFile(String filePath);
}
