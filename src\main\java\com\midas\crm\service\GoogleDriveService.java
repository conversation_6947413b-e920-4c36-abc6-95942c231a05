package com.midas.crm.service;

import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * Servicio para operaciones con Google Drive
 */
public interface GoogleDriveService {

    /**
     * Sube un archivo a Google Drive
     * @param file Archivo a subir
     * @param fileName Nombre del archivo
     * @param folderId ID de la carpeta destino (opcional)
     * @return ID del archivo subido
     */
    String uploadFile(MultipartFile file, String fileName, String folderId) throws IOException;

    /**
     * Sube un archivo a Google Drive en la carpeta raíz
     * @param file Archivo a subir
     * @param fileName Nombre del archivo
     * @return ID del archivo subido
     */
    String uploadFile(MultipartFile file, String fileName) throws IOException;

    /**
     * Descarga un archivo de Google Drive
     * @param fileId ID del archivo
     * @return Contenido del archivo como array de bytes
     */
    byte[] downloadFile(String fileId) throws IOException;

    /**
     * Elimina un archivo de Google Drive
     * @param fileId ID del archivo a eliminar
     */
    void deleteFile(String fileId) throws IOException;

    /**
     * Lista archivos en Google Drive
     * @param folderId ID de la carpeta (opcional)
     * @param pageSize Número máximo de archivos a retornar
     * @return Lista de archivos
     */
    List<Map<String, Object>> listFiles(String folderId, int pageSize) throws IOException;

    /**
     * Lista archivos en la carpeta raíz
     * @param pageSize Número máximo de archivos a retornar
     * @return Lista de archivos
     */
    List<Map<String, Object>> listFiles(int pageSize) throws IOException;

    /**
     * Crea una carpeta en Google Drive
     * @param folderName Nombre de la carpeta
     * @param parentFolderId ID de la carpeta padre (opcional)
     * @return ID de la carpeta creada
     */
    String createFolder(String folderName, String parentFolderId) throws IOException;

    /**
     * Crea una carpeta en la raíz de Google Drive
     * @param folderName Nombre de la carpeta
     * @return ID de la carpeta creada
     */
    String createFolder(String folderName) throws IOException;

    /**
     * Obtiene información de un archivo
     * @param fileId ID del archivo
     * @return Información del archivo
     */
    Map<String, Object> getFileInfo(String fileId) throws IOException;

    /**
     * Comparte un archivo con permisos específicos
     * @param fileId ID del archivo
     * @param email Email del usuario con quien compartir
     * @param role Rol del usuario (reader, writer, owner)
     * @return ID del permiso creado
     */
    String shareFile(String fileId, String email, String role) throws IOException;

    /**
     * Obtiene el enlace público de un archivo
     * @param fileId ID del archivo
     * @return URL pública del archivo
     */
    String getPublicLink(String fileId) throws IOException;

    /**
     * Busca archivos MP3 por número de agente
     * @param numeroAgente Número del agente (normalizado)
     * @return Lista de archivos MP3 encontrados
     */
    List<Map<String, Object>> findMp3FilesByAgent(String numeroAgente) throws IOException;

    /**
     * Busca archivos MP3 por nombre que contenga el número de agente
     * @param numeroAgente Número del agente
     * @param folderId ID de la carpeta donde buscar (opcional)
     * @return URL del primer archivo MP3 encontrado o null si no se encuentra
     */
    String findMp3UrlByAgent(String numeroAgente, String folderId) throws IOException;

    /**
     * Busca archivo de audio por número móvil y agente (más preciso)
     * @param numeroMovil Número móvil del cliente
     * @param numeroAgente Número del agente
     * @param folderId ID de la carpeta donde buscar (opcional)
     * @return URL del archivo de audio encontrado o null si no se encuentra
     */
    String findAudioByMovilAndAgent(String numeroMovil, String numeroAgente, String folderId) throws IOException;

    /**
     * Busca una carpeta por nombre
     * @param folderName Nombre de la carpeta a buscar
     * @return ID de la carpeta si se encuentra, null si no existe
     */
    String findFolderByName(String folderName) throws IOException;

    /**
     * Lista todas las carpetas disponibles
     * @param pageSize Número máximo de carpetas a retornar
     * @return Lista de carpetas
     */
    List<Map<String, Object>> listFolders(int pageSize) throws IOException;
}
