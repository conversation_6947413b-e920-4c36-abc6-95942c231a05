package com.midas.crm.service.serviceImpl;

import com.midas.crm.config.RabbitMQConfig;
import com.midas.crm.entity.ClienteResidencial;
import com.midas.crm.entity.DTO.queue.TranscriptionQueueMessage;
import com.midas.crm.repository.ClienteResidencialRepository;
import com.midas.crm.service.TranscriptionQueueService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * Implementación del servicio de colas de transcripción
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TranscriptionQueueServiceImpl implements TranscriptionQueueService {

    private final ClienteResidencialRepository clienteResidencialRepository;
    private final RabbitTemplate rabbitTemplate;
    private final RestTemplate restTemplate = new RestTemplate();

    // Control de estado del procesamiento
    private final AtomicBoolean processingActive = new AtomicBoolean(true);

    // URLs de las APIs externas
    @Value("${transcription.api.url:https://apisozarusac.com/BackendTranscriptor/api}")
    private String transcriptionApiUrl;

    @Value("${comparison.api.url:https://apisozarusac.com/ventas/api}")
    private String comparisonApiUrl;

    @Value("${google.drive.api.url:http://localhost:9039/api/google-drive}")
    private String googleDriveApiUrl;

    @Override
    public Map<String, Object> processPendingLeads(int batchSize, String numeroAgente) {
        log.info("Procesando leads pendientes. BatchSize: {}, NumeroAgente: {}", batchSize, numeroAgente);

        Map<String, Object> result = new HashMap<>();

        try {
            // Obtener leads pendientes
            List<ClienteResidencial> pendingLeads = getPendingLeadsFromDatabase(batchSize, numeroAgente);

            int totalFound = pendingLeads.size();
            int sentToQueue = 0;
            int errors = 0;
            List<String> errorMessages = new ArrayList<>();

            for (ClienteResidencial lead : pendingLeads) {
                try {
                    if (sendLeadToTranscriptionQueue(lead)) {
                        sentToQueue++;
                    } else {
                        errors++;
                        errorMessages.add("Error al enviar lead ID: " + lead.getId());
                    }
                } catch (Exception e) {
                    errors++;
                    errorMessages.add("Error al procesar lead ID " + lead.getId() + ": " + e.getMessage());
                    log.error("Error al procesar lead {}", lead.getId(), e);
                }
            }

            result.put("totalFound", totalFound);
            result.put("sentToQueue", sentToQueue);
            result.put("errors", errors);
            result.put("errorMessages", errorMessages);
            result.put("timestamp", LocalDateTime.now());

            log.info("Procesamiento completado. Total: {}, Enviados: {}, Errores: {}",
                    totalFound, sentToQueue, errors);

        } catch (Exception e) {
            log.error("Error general al procesar leads pendientes", e);
            result.put("error", "Error general: " + e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> getQueueStatistics() {
        Map<String, Object> stats = new HashMap<>();

        try {
            // Obtener estadísticas de la base de datos
            long totalLeads = clienteResidencialRepository.count();
            long leadsWithTranscription = clienteResidencialRepository.countByTextoTranscriptionIsNotNull();
            long leadsWithoutTranscription = totalLeads - leadsWithTranscription;
            long leadsWithNotes = clienteResidencialRepository.countByNotaAgenteComparadorIAIsNotNull();

            stats.put("totalLeads", totalLeads);
            stats.put("leadsWithTranscription", leadsWithTranscription);
            stats.put("leadsWithoutTranscription", leadsWithoutTranscription);
            stats.put("leadsWithNotes", leadsWithNotes);
            stats.put("processingActive", processingActive.get());
            stats.put("timestamp", LocalDateTime.now());

            // Estadísticas por agente
            List<Object[]> agentStats = clienteResidencialRepository.getTranscriptionStatsByAgent();
            Map<String, Map<String, Long>> agentStatsMap = new HashMap<>();

            for (Object[] stat : agentStats) {
                String agente = (String) stat[0];
                Long total = (Long) stat[1];
                Long withTranscription = (Long) stat[2];

                Map<String, Long> agentData = new HashMap<>();
                agentData.put("total", total);
                agentData.put("withTranscription", withTranscription);
                agentData.put("withoutTranscription", total - withTranscription);

                agentStatsMap.put(agente != null ? agente : "SIN_AGENTE", agentData);
            }

            stats.put("statsByAgent", agentStatsMap);

        } catch (Exception e) {
            log.error("Error al obtener estadísticas de cola", e);
            stats.put("error", "Error al obtener estadísticas: " + e.getMessage());
        }

        return stats;
    }

    @Override
    public List<TranscriptionQueueMessage> getPendingLeads(int limit, String numeroAgente) {
        List<ClienteResidencial> pendingLeads = getPendingLeadsFromDatabase(limit, numeroAgente);

        return pendingLeads.stream()
                .map(this::convertToQueueMessage)
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, Object> retryFailedMessages(int maxRetries) {
        Map<String, Object> result = new HashMap<>();

        // TODO: Implementar lógica para obtener mensajes de la DLQ y reenviarlos
        // Por ahora, retornamos estadísticas básicas

        result.put("message", "Funcionalidad de reintento en desarrollo");
        result.put("maxRetries", maxRetries);
        result.put("timestamp", LocalDateTime.now());

        return result;
    }

    @Override
    public boolean sendLeadToQueue(Long leadId) {
        try {
            Optional<ClienteResidencial> leadOpt = clienteResidencialRepository.findById(leadId);

            if (leadOpt.isPresent()) {
                return sendLeadToTranscriptionQueue(leadOpt.get());
            } else {
                log.warn("Lead con ID {} no encontrado", leadId);
                return false;
            }

        } catch (Exception e) {
            log.error("Error al enviar lead {} a la cola", leadId, e);
            return false;
        }
    }

    @Override
    public void pauseProcessing() {
        processingActive.set(false);
        log.info("Procesamiento de colas pausado");
    }

    @Override
    public void resumeProcessing() {
        processingActive.set(true);
        log.info("Procesamiento de colas reanudado");
    }

    @Override
    public String normalizeAgentNumber(String numeroAgente) {
        if (numeroAgente == null || numeroAgente.trim().isEmpty()) {
            return null;
        }

        String agente = numeroAgente.trim().toLowerCase();

        // Remover prefijo "agen" si existe
        if (agente.startsWith("agen")) {
            agente = agente.substring(4);
        }

        // Remover ceros a la izquierda
        agente = agente.replaceFirst("^0+", "");

        // Si quedó vacío después de remover ceros, devolver "0"
        if (agente.isEmpty()) {
            agente = "0";
        }

        // Validar que solo contenga números
        if (!agente.matches("\\d+")) {
            return numeroAgente; // Devolver original si no es numérico
        }

        return agente;
    }

    @Override
    public boolean isLeadEligibleForProcessing(Long leadId) {
        try {
            Optional<ClienteResidencial> leadOpt = clienteResidencialRepository.findById(leadId);

            if (leadOpt.isPresent()) {
                ClienteResidencial lead = leadOpt.get();

                // Verificar que tenga número móvil
                if (lead.getMovilContacto() == null || lead.getMovilContacto().trim().isEmpty()) {
                    return false;
                }

                // Verificar que no tenga transcripción
                if (lead.getTextoTranscription() != null && !lead.getTextoTranscription().trim().isEmpty()) {
                    return false;
                }

                // Verificar que tenga número de agente
                if (lead.getNumeroAgente() == null || lead.getNumeroAgente().trim().isEmpty()) {
                    return false;
                }

                return true;
            }

            return false;

        } catch (Exception e) {
            log.error("Error al verificar elegibilidad del lead {}", leadId, e);
            return false;
        }
    }

    @Override
    public boolean isProcessingActive() {
        return processingActive.get();
    }

    // ========== MÉTODOS PRIVADOS ==========

    /**
     * Obtiene leads pendientes de la base de datos
     */
    private List<ClienteResidencial> getPendingLeadsFromDatabase(int limit, String numeroAgente) {
        try {
            if (numeroAgente != null && !numeroAgente.trim().isEmpty()) {
                String normalizedAgent = normalizeAgentNumber(numeroAgente);
                return clienteResidencialRepository.findLeadsWithoutTranscriptionByAgent(normalizedAgent, limit);
            } else {
                return clienteResidencialRepository.findLeadsWithoutTranscription(limit);
            }
        } catch (Exception e) {
            log.error("Error al obtener leads pendientes de la base de datos", e);
            return new ArrayList<>();
        }
    }

    /**
     * Envía un lead a la cola de transcripción
     */
    private boolean sendLeadToTranscriptionQueue(ClienteResidencial lead) {
        try {
            TranscriptionQueueMessage message = convertToQueueMessage(lead);

            rabbitTemplate.convertAndSend(
                    RabbitMQConfig.TRANSCRIPTION_EXCHANGE,
                    RabbitMQConfig.TRANSCRIPTION_ROUTING_KEY,
                    message
            );

            log.debug("Lead {} enviado a la cola de transcripción", lead.getId());
            return true;

        } catch (Exception e) {
            log.error("Error al enviar lead {} a la cola de transcripción", lead.getId(), e);
            return false;
        }
    }

    /**
     * Convierte un ClienteResidencial a TranscriptionQueueMessage
     */
    private TranscriptionQueueMessage convertToQueueMessage(ClienteResidencial lead) {
        return TranscriptionQueueMessage.builder()
                .leadId(lead.getId())
                .numeroMovil(lead.getMovilContacto())
                .numeroAgente(lead.getNumeroAgente())
                .numeroAgenteNormalizado(normalizeAgentNumber(lead.getNumeroAgente()))
                .nombreCliente(lead.getNombresApellidos())
                .dniCliente(lead.getNifNie())
                .fechaCreacion(lead.getFechaCreacion())
                .nombreAsesor(lead.getUsuario() != null ? lead.getUsuario().getNombres() : null)
                .archivoMp3Url(null) // Se establecerá cuando se obtenga de Google Drive
                .nombreArchivoMp3(lead.getNombreArchivoMp3())
                .estadoProcesamiento("PENDING")
                .fechaEnvio(LocalDateTime.now())
                .intentos(0)
                .maxIntentos(3)
                .queueName(RabbitMQConfig.TRANSCRIPTION_QUEUE)
                .routingKey(RabbitMQConfig.TRANSCRIPTION_ROUTING_KEY)
                .build();
    }

    // ========== LISTENERS DE RABBITMQ ==========

    /**
     * Procesa mensajes de la cola de transcripción
     */
    @RabbitListener(queues = RabbitMQConfig.TRANSCRIPTION_QUEUE)
    public void processTranscriptionMessage(TranscriptionQueueMessage message) {
        if (!processingActive.get()) {
            log.info("Procesamiento pausado, reenvíando mensaje a la cola");
            // Reenviar el mensaje a la cola para procesarlo más tarde
            rabbitTemplate.convertAndSend(
                    RabbitMQConfig.TRANSCRIPTION_EXCHANGE,
                    RabbitMQConfig.TRANSCRIPTION_ROUTING_KEY,
                    message
            );
            return;
        }

        log.info("Procesando mensaje de transcripción para lead ID: {}", message.getLeadId());

        try {
            message.marcarComoProcesando();
            message.incrementarIntentos();

            // 1. Buscar archivo MP3 en Google Drive
            String mp3Url = findMp3FileInGoogleDrive(message);
            if (mp3Url == null) {
                throw new RuntimeException("No se encontró archivo MP3 para el lead");
            }
            message.setArchivoMp3Url(mp3Url);

            // 2. Llamar a la API de transcripción
            Map<String, Object> transcriptionResult = callTranscriptionAPI(message);

            // 3. Guardar resultado en la base de datos
            saveTranscriptionResult(message, transcriptionResult);

            // 4. Enviar a cola de comparación
            sendToComparisonQueue(message, transcriptionResult);

            message.marcarComoCompletado();
            log.info("Transcripción completada para lead ID: {}", message.getLeadId());

        } catch (Exception e) {
            log.error("Error al procesar transcripción para lead ID: {}", message.getLeadId(), e);

            message.marcarComoFallido(e.getMessage());

            if (!message.intentosAgotados()) {
                // Reenviar a la cola para reintento
                log.info("Reenviando mensaje para reintento. Intento: {}/{}",
                        message.getIntentos(), message.getMaxIntentos());

                rabbitTemplate.convertAndSend(
                        RabbitMQConfig.TRANSCRIPTION_EXCHANGE,
                        RabbitMQConfig.TRANSCRIPTION_ROUTING_KEY,
                        message
                );
            } else {
                log.error("Intentos agotados para lead ID: {}. Enviando a DLQ", message.getLeadId());
                // El mensaje irá automáticamente a la DLQ por configuración
            }
        }
    }

    /**
     * Procesa mensajes de la cola de comparación
     */
    @RabbitListener(queues = RabbitMQConfig.COMPARISON_QUEUE)
    public void processComparisonMessage(TranscriptionQueueMessage message) {
        if (!processingActive.get()) {
            log.info("Procesamiento pausado, reenvíando mensaje de comparación a la cola");
            rabbitTemplate.convertAndSend(
                    RabbitMQConfig.COMPARISON_EXCHANGE,
                    RabbitMQConfig.COMPARISON_ROUTING_KEY,
                    message
            );
            return;
        }

        log.info("Procesando mensaje de comparación para lead ID: {}", message.getLeadId());

        try {
            message.incrementarIntentos();

            // Llamar a la API de comparación
            Map<String, Object> comparisonResult = callComparisonAPI(message);

            // Guardar resultado de comparación
            saveComparisonResult(message, comparisonResult);

            log.info("Comparación completada para lead ID: {}", message.getLeadId());

        } catch (Exception e) {
            log.error("Error al procesar comparación para lead ID: {}", message.getLeadId(), e);

            if (!message.intentosAgotados()) {
                log.info("Reenviando mensaje de comparación para reintento. Intento: {}/{}",
                        message.getIntentos(), message.getMaxIntentos());

                rabbitTemplate.convertAndSend(
                        RabbitMQConfig.COMPARISON_EXCHANGE,
                        RabbitMQConfig.COMPARISON_ROUTING_KEY,
                        message
                );
            } else {
                log.error("Intentos agotados para comparación de lead ID: {}", message.getLeadId());
            }
        }
    }

    // ========== MÉTODOS DE INTEGRACIÓN CON APIS EXTERNAS ==========

    /**
     * Busca archivo de audio en Google Drive basado en el número móvil y agente
     */
    private String findMp3FileInGoogleDrive(TranscriptionQueueMessage message) {
        try {
            String numeroAgenteNormalizado = message.getNumeroAgenteNormalizado();
            String numeroMovil = message.getNumeroMovil();

            if (numeroAgenteNormalizado == null || numeroAgenteNormalizado.trim().isEmpty()) {
                log.warn("Número de agente normalizado vacío para lead ID: {}", message.getLeadId());
                return null;
            }

            log.info("Buscando archivo de audio para móvil {} y agente {} (lead ID: {})",
                    numeroMovil, numeroAgenteNormalizado, message.getLeadId());

            // Buscar archivo de audio usando el servicio de Google Drive
            // Primero intentar búsqueda precisa por móvil y agente
            if (numeroMovil != null && !numeroMovil.trim().isEmpty()) {
                try {
                    // Llamar al endpoint de Google Drive para buscar por móvil y agente
                    String audioUrl = callGoogleDriveSearchAPI(numeroMovil, numeroAgenteNormalizado);
                    if (audioUrl != null) {
                        log.info("Archivo de audio encontrado por móvil y agente: {}", audioUrl);
                        return audioUrl;
                    }
                } catch (Exception e) {
                    log.warn("Error al buscar por móvil y agente: {}", e.getMessage());
                }
            }

            // Si no se encuentra por móvil, buscar solo por agente
            try {
                String mp3Url = callGoogleDriveAgentSearchAPI(numeroAgenteNormalizado);
                if (mp3Url != null) {
                    log.info("Archivo MP3 encontrado para agente {}: {}", numeroAgenteNormalizado, mp3Url);
                    return mp3Url;
                }
            } catch (Exception e) {
                log.warn("Error al buscar por agente: {}", e.getMessage());
            }

            log.warn("No se encontró archivo de audio para móvil {} y agente {} (lead ID: {})",
                    numeroMovil, numeroAgenteNormalizado, message.getLeadId());
            return null;

        } catch (Exception e) {
            log.error("Error al buscar archivo de audio en Google Drive para lead ID: {}", message.getLeadId(), e);
            return null;
        }
    }

    /**
     * Llama a la API de Google Drive para buscar archivo por móvil y agente
     */
    private String callGoogleDriveSearchAPI(String numeroMovil, String numeroAgente) {
        try {
            String url = googleDriveApiUrl + "/audio-url?numeroMovil=" + numeroMovil + "&numeroAgente=" + numeroAgente;

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> entity = new HttpEntity<>(headers);

            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.GET, entity, Map.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                Integer rpta = (Integer) responseBody.get("rpta");
                if (rpta != null && rpta == 1) {
                    Map<String, Object> data = (Map<String, Object>) responseBody.get("data");
                    if (data != null) {
                        return (String) data.get("audioUrl");
                    }
                }
            }

            return null;
        } catch (Exception e) {
            log.error("Error al llamar API de Google Drive para búsqueda por móvil y agente", e);
            return null;
        }
    }

    /**
     * Llama a la API de Google Drive para buscar archivo solo por agente
     */
    private String callGoogleDriveAgentSearchAPI(String numeroAgente) {
        try {
            String url = googleDriveApiUrl + "/mp3-url/" + numeroAgente;

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> entity = new HttpEntity<>(headers);

            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.GET, entity, Map.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                Integer rpta = (Integer) responseBody.get("rpta");
                if (rpta != null && rpta == 1) {
                    Map<String, Object> data = (Map<String, Object>) responseBody.get("data");
                    if (data != null) {
                        return (String) data.get("mp3Url");
                    }
                }
            }

            return null;
        } catch (Exception e) {
            log.error("Error al llamar API de Google Drive para búsqueda por agente", e);
            return null;
        }
    }

    /**
     * Llama a la API de transcripción externa
     */
    private Map<String, Object> callTranscriptionAPI(TranscriptionQueueMessage message) {
        try {
            // Preparar headers
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // Preparar datos para la API
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("audio_url", message.getArchivoMp3Url());
            requestData.put("whisper_model", message.getWhisperModel());
            requestData.put("device", message.getDevice());
            requestData.put("target_language", message.getTargetLanguage());
            requestData.put("call_type", message.getCallType());
            requestData.put("call_id", message.getCallId());
            requestData.put("caller_phone", message.getNumeroMovil());
            requestData.put("agent_id", message.getAgentId());
            requestData.put("call_datetime", message.getCallDatetime());

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestData, headers);

            // Llamar a la API
            String url = transcriptionApiUrl + "/transcribe/";
            ResponseEntity<Map> response = restTemplate.postForEntity(url, entity, Map.class);

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                return response.getBody();
            } else {
                throw new RuntimeException("API de transcripción retornó estado: " + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("Error al llamar API de transcripción para lead ID: {}", message.getLeadId(), e);
            throw new RuntimeException("Error en API de transcripción: " + e.getMessage(), e);
        }
    }

    /**
     * Llama a la API de comparación externa
     */
    private Map<String, Object> callComparisonAPI(TranscriptionQueueMessage message) {
        try {
            // Obtener datos del lead para comparación
            Optional<ClienteResidencial> leadOpt = clienteResidencialRepository.findById(message.getLeadId());
            if (!leadOpt.isPresent()) {
                throw new RuntimeException("Lead no encontrado para comparación");
            }

            ClienteResidencial lead = leadOpt.get();

            // Preparar headers
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // Preparar datos para la API de comparación
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("texto_audio", lead.getTextoTranscription());

            Map<String, Object> datosLead = new HashMap<>();
            datosLead.put("nombres", lead.getNombresApellidos());
            datosLead.put("numeroMovil", lead.getMovilContacto());
            datosLead.put("dni", lead.getNifNie());
            datosLead.put("fechaCreacion", lead.getFechaCreacion().toString());
            datosLead.put("numeroAgente", lead.getNumeroAgente());

            requestData.put("datos_lead", datosLead);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestData, headers);

            // Llamar a la API
            String url = comparisonApiUrl + "/comparar/";
            ResponseEntity<Map> response = restTemplate.postForEntity(url, entity, Map.class);

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                return response.getBody();
            } else {
                throw new RuntimeException("API de comparación retornó estado: " + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("Error al llamar API de comparación para lead ID: {}", message.getLeadId(), e);
            throw new RuntimeException("Error en API de comparación: " + e.getMessage(), e);
        }
    }

    /**
     * Guarda el resultado de la transcripción en la base de datos
     */
    private void saveTranscriptionResult(TranscriptionQueueMessage message, Map<String, Object> transcriptionResult) {
        try {
            Optional<ClienteResidencial> leadOpt = clienteResidencialRepository.findById(message.getLeadId());
            if (!leadOpt.isPresent()) {
                throw new RuntimeException("Lead no encontrado para guardar transcripción");
            }

            ClienteResidencial lead = leadOpt.get();

            // Extraer texto de transcripción del resultado
            String transcriptionText = (String) transcriptionResult.get("transcription");
            if (transcriptionText != null) {
                lead.setTextoTranscription(transcriptionText);
            }

            // Guardar nombre del archivo MP3 si está disponible
            if (message.getNombreArchivoMp3() != null) {
                lead.setNombreArchivoMp3(message.getNombreArchivoMp3());
            }

            clienteResidencialRepository.save(lead);
            log.info("Resultado de transcripción guardado para lead ID: {}", message.getLeadId());

        } catch (Exception e) {
            log.error("Error al guardar resultado de transcripción para lead ID: {}", message.getLeadId(), e);
            throw new RuntimeException("Error al guardar transcripción: " + e.getMessage(), e);
        }
    }

    /**
     * Guarda el resultado de la comparación en la base de datos
     */
    private void saveComparisonResult(TranscriptionQueueMessage message, Map<String, Object> comparisonResult) {
        try {
            Optional<ClienteResidencial> leadOpt = clienteResidencialRepository.findById(message.getLeadId());
            if (!leadOpt.isPresent()) {
                throw new RuntimeException("Lead no encontrado para guardar comparación");
            }

            ClienteResidencial lead = leadOpt.get();

            // Extraer porcentaje de coincidencia del resultado
            Object porcentajeObj = comparisonResult.get("porcentaje_coincidencia");
            if (porcentajeObj != null) {
                Double porcentaje = null;
                if (porcentajeObj instanceof Number) {
                    porcentaje = ((Number) porcentajeObj).doubleValue();
                } else if (porcentajeObj instanceof String) {
                    try {
                        porcentaje = Double.parseDouble((String) porcentajeObj);
                    } catch (NumberFormatException e) {
                        log.warn("No se pudo convertir porcentaje a número: {}", porcentajeObj);
                    }
                }

                if (porcentaje != null) {
                    lead.setNotaAgenteComparadorIA(java.math.BigDecimal.valueOf(porcentaje));
                }
            }

            clienteResidencialRepository.save(lead);
            log.info("Resultado de comparación guardado para lead ID: {}", message.getLeadId());

        } catch (Exception e) {
            log.error("Error al guardar resultado de comparación para lead ID: {}", message.getLeadId(), e);
            throw new RuntimeException("Error al guardar comparación: " + e.getMessage(), e);
        }
    }

    /**
     * Envía mensaje a la cola de comparación
     */
    private void sendToComparisonQueue(TranscriptionQueueMessage message, Map<String, Object> transcriptionResult) {
        try {
            // Resetear intentos para la comparación
            message.setIntentos(0);
            message.setEstadoProcesamiento("PENDING");

            rabbitTemplate.convertAndSend(
                    RabbitMQConfig.COMPARISON_EXCHANGE,
                    RabbitMQConfig.COMPARISON_ROUTING_KEY,
                    message
            );

            log.debug("Mensaje enviado a cola de comparación para lead ID: {}", message.getLeadId());

        } catch (Exception e) {
            log.error("Error al enviar mensaje a cola de comparación para lead ID: {}", message.getLeadId(), e);
            throw new RuntimeException("Error al enviar a cola de comparación: " + e.getMessage(), e);
        }
    }
}
