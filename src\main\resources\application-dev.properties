# Configuración específica para DESARROLLO LOCAL
spring.application.name=crm
server.port=9039
server.address=0.0.0.0

# Configuración de seguridad para desarrollo
server.ssl.enabled=false

# MYSQL REMOTO - Configuraci�n optimizada para alta concurrencia
spring.datasource.url=********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
spring.datasource.username=usuarioCrm2
spring.datasource.password=Midas*2025%
spring.jackson.time-zone=America/Lima
spring.jpa.properties.hibernate.jdbc.time_zone=UTC
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

#Configurando Pool de conecciones
#maximum number of milliseconds that a client will wait for a connection
spring.datasource.hikari.connection-timeout = 20000
#minimum number of idle connections maintained by HikariCP in a connection pool
spring.datasource.hikari.minimum-idle= 1
#maximum pool size
spring.datasource.hikari.maximum-pool-size= 10
#maximum idle time for connection
spring.datasource.hikari.idle-timeout=10000
# maximum lifetime in milliseconds of a connection in the pool after it is closed.
spring.datasource.hikari.max-lifetime= 1000

#size por defecto de toda la aplicacion
spring.servlet.multipart.max-file-size=200MB
spring.servlet.multipart.max-request-size=200MB

#Parametros ThreadPoolTaskExecutor
threadpool.corepoolsize=7
threadpool.maxpoolsize=15
threadpool.queuecapacity=25

# Configuraci�n de JPA para desarrollo
spring.jpa.hibernate.ddl-auto=update

# OAuth Configuration
# Para desarrollo local (se sobrescribe en application-prod.properties para producción)
google.drive.auth.type=oauth
google.drive.credentials.file=client_secret_com.json
google.drive.oauth.redirect-uri=http://localhost:9039/oauth2/callback
google.drive.oauth.tokens.file=tokens.json

# ===== CONFIGURACIÓN DE RABBITMQ =====
# Configuración básica de RabbitMQ
spring.rabbitmq.host=localhost
spring.rabbitmq.port=5672
spring.rabbitmq.username=midasuser
spring.rabbitmq.password=MidasRabbit2025!
spring.rabbitmq.virtual-host=/midas